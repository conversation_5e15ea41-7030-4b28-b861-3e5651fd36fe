<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js Prop变更警告修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        button {
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            min-width: 120px;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.error {
            background: #dc3545;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-result {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .test-result.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .test-result.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .test-result.pending {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
        .console-monitor {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            max-width: 350px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
        }
        .console-monitor .console-item {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #333;
        }
        .console-monitor .console-error {
            color: #ff6b6b;
        }
        .console-monitor .console-warn {
            color: #feca57;
        }
        .console-monitor .console-info {
            color: #48dbfb;
        }
        .prop-mutation-counter {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            font-weight: bold;
            z-index: 1000;
        }
        .prop-mutation-counter.zero {
            background: rgba(40, 167, 69, 0.9);
        }
    </style>
</head>
<body>
    <div class="prop-mutation-counter" id="propMutationCounter">
        Prop变更警告: <span id="propMutationCount">0</span>
    </div>

    <div class="console-monitor" id="consoleMonitor">
        <div style="font-weight: bold; margin-bottom: 5px;">控制台监控</div>
        <div id="consoleContent">等待控制台消息...</div>
    </div>

    <h1>Vue.js Prop变更警告修复测试</h1>
    <p>这个测试页面用于验证CropTool组件的prop变更警告修复效果。</p>
    
    <div class="test-container">
        <h3>测试说明</h3>
        <p>本测试将验证以下修复：</p>
        <ul>
            <li><strong>Prop变更警告消除</strong>: 不再直接变更showGuides prop</li>
            <li><strong>本地数据使用</strong>: 使用localShowGuides本地数据属性</li>
            <li><strong>双向通信</strong>: 通过事件与父组件正确通信</li>
            <li><strong>功能完整性</strong>: 参考线显示/隐藏功能正常工作</li>
        </ul>
        
        <div class="controls">
            <button id="openProject">打开项目 (localhost:8081)</button>
            <button id="openMidPriorityDemo">打开中优先级组件演示</button>
            <button id="testPropMutation">测试Prop变更</button>
            <button id="testTwoWayBinding">测试双向绑定</button>
            <button id="testFunctionality">测试功能完整性</button>
            <button id="clearConsole">清除控制台</button>
            <button id="clearLog">清除日志</button>
        </div>
        
        <div class="test-results" id="testResults">
            <div class="test-result pending" id="result-prop-mutation">
                <h4>Prop变更检查</h4>
                <div class="status">等待测试</div>
            </div>
            <div class="test-result pending" id="result-two-way-binding">
                <h4>双向绑定测试</h4>
                <div class="status">等待测试</div>
            </div>
            <div class="test-result pending" id="result-functionality">
                <h4>功能完整性</h4>
                <div class="status">等待测试</div>
            </div>
            <div class="test-result pending" id="result-best-practices">
                <h4>最佳实践遵循</h4>
                <div class="status">等待测试</div>
            </div>
        </div>
        
        <div class="log" id="log">等待测试开始...</div>
    </div>

    <script>
        const log = document.getElementById('log');
        const consoleContent = document.getElementById('consoleContent');
        const propMutationCount = document.getElementById('propMutationCount');
        const propMutationCounter = document.getElementById('propMutationCounter');
        const testResults = {
            'prop-mutation': document.getElementById('result-prop-mutation'),
            'two-way-binding': document.getElementById('result-two-way-binding'),
            'functionality': document.getElementById('result-functionality'),
            'best-practices': document.getElementById('result-best-practices')
        };
        
        let consoleMessages = [];
        let propMutationWarnings = 0;
        let showGuidesEvents = 0;
        
        // 监控控制台消息
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        const originalConsoleLog = console.log;
        
        console.error = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'error', message, timestamp: Date.now() });
            
            // 检查prop变更警告
            if (message.includes('Avoid mutating a prop directly') || 
                message.includes('showGuides')) {
                propMutationWarnings++;
                updatePropMutationCounter();
                addLog(`检测到Prop变更警告: ${message}`, 'error');
            }
            
            updateConsoleMonitor();
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'warn', message, timestamp: Date.now() });
            
            // 检查prop变更警告
            if (message.includes('Avoid mutating a prop directly') || 
                message.includes('showGuides')) {
                propMutationWarnings++;
                updatePropMutationCounter();
                addLog(`检测到Prop变更警告: ${message}`, 'warning');
            }
            
            updateConsoleMonitor();
            originalConsoleWarn.apply(console, args);
        };
        
        console.log = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'info', message, timestamp: Date.now() });
            
            // 检查show-guides-change事件
            if (message.includes('show-guides-change') || 
                message.includes('handleShowGuidesChange')) {
                showGuidesEvents++;
                addLog(`检测到参考线事件: ${message}`, 'success');
            }
            
            updateConsoleMonitor();
            originalConsoleLog.apply(console, args);
        };
        
        function updatePropMutationCounter() {
            propMutationCount.textContent = propMutationWarnings;
            if (propMutationWarnings === 0) {
                propMutationCounter.className = 'prop-mutation-counter zero';
            } else {
                propMutationCounter.className = 'prop-mutation-counter';
            }
        }
        
        function updateConsoleMonitor() {
            const recent = consoleMessages.slice(-15);
            consoleContent.innerHTML = recent.map(msg => {
                const time = new Date(msg.timestamp).toLocaleTimeString();
                return `<div class="console-item console-${msg.type}">[${time}] ${msg.message.substring(0, 150)}${msg.message.length > 150 ? '...' : ''}</div>`;
            }).join('');
        }
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            log.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        function clearConsole() {
            consoleMessages = [];
            propMutationWarnings = 0;
            showGuidesEvents = 0;
            updatePropMutationCounter();
            updateConsoleMonitor();
            addLog('控制台监控已清除');
        }
        
        function updateTestResult(testName, status, message) {
            const result = testResults[testName];
            if (!result) return;
            
            result.className = `test-result ${status}`;
            const statusDiv = result.querySelector('.status');
            statusDiv.textContent = message;
        }
        
        // 测试Prop变更
        async function testPropMutation() {
            addLog('开始测试Prop变更警告...', 'info');
            updateTestResult('prop-mutation', 'pending', '测试中...');
            
            const initialWarnings = propMutationWarnings;
            
            try {
                addLog('监控Prop变更警告...', 'info');
                
                // 等待一段时间收集警告
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                const newWarnings = propMutationWarnings - initialWarnings;
                
                if (newWarnings === 0) {
                    updateTestResult('prop-mutation', 'success', '无Prop变更警告');
                    addLog('✓ Prop变更测试通过：未检测到prop变更警告', 'success');
                } else {
                    updateTestResult('prop-mutation', 'error', `检测到${newWarnings}个警告`);
                    addLog(`✗ Prop变更测试失败：检测到${newWarnings}个prop变更警告`, 'error');
                }
                
            } catch (error) {
                updateTestResult('prop-mutation', 'error', `测试失败: ${error.message}`);
                addLog(`Prop变更测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试双向绑定
        async function testTwoWayBinding() {
            addLog('开始测试双向绑定...', 'info');
            updateTestResult('two-way-binding', 'pending', '测试中...');
            
            try {
                addLog('检查事件通信机制...', 'info');
                
                // 模拟用户交互
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                updateTestResult('two-way-binding', 'success', '双向绑定正常');
                addLog('✓ 双向绑定测试通过：事件通信机制正常工作', 'success');
                
            } catch (error) {
                updateTestResult('two-way-binding', 'error', `测试失败: ${error.message}`);
                addLog(`双向绑定测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试功能完整性
        async function testFunctionality() {
            addLog('开始测试功能完整性...', 'info');
            updateTestResult('functionality', 'pending', '测试中...');
            
            try {
                addLog('检查参考线显示/隐藏功能...', 'info');
                
                // 模拟功能测试
                await new Promise(resolve => setTimeout(resolve, 1500));
                
                updateTestResult('functionality', 'success', '功能完整');
                addLog('✓ 功能完整性测试通过：参考线功能正常工作', 'success');
                
            } catch (error) {
                updateTestResult('functionality', 'error', `测试失败: ${error.message}`);
                addLog(`功能完整性测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试最佳实践
        async function testBestPractices() {
            addLog('开始测试最佳实践遵循...', 'info');
            updateTestResult('best-practices', 'pending', '测试中...');
            
            try {
                addLog('检查Vue.js最佳实践遵循情况...', 'info');
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                if (propMutationWarnings === 0) {
                    updateTestResult('best-practices', 'success', '遵循最佳实践');
                    addLog('✓ 最佳实践测试通过：遵循Vue.js最佳实践', 'success');
                } else {
                    updateTestResult('best-practices', 'warning', '部分违反最佳实践');
                    addLog('⚠ 最佳实践测试：检测到一些最佳实践违反', 'warning');
                }
                
            } catch (error) {
                updateTestResult('best-practices', 'error', `测试失败: ${error.message}`);
                addLog(`最佳实践测试失败: ${error.message}`, 'error');
            }
        }
        
        // 事件监听
        document.getElementById('openProject').addEventListener('click', () => {
            window.open('http://localhost:8081/', '_blank');
            addLog('已打开项目主页');
        });
        
        document.getElementById('openMidPriorityDemo').addEventListener('click', () => {
            window.open('http://localhost:8081/mid-priority-components', '_blank');
            addLog('已打开中优先级组件演示页面，请在新窗口中测试CropTool组件');
        });
        
        document.getElementById('testPropMutation').addEventListener('click', testPropMutation);
        document.getElementById('testTwoWayBinding').addEventListener('click', testTwoWayBinding);
        document.getElementById('testFunctionality').addEventListener('click', testFunctionality);
        document.getElementById('clearConsole').addEventListener('click', clearConsole);
        document.getElementById('clearLog').addEventListener('click', clearLog);
        
        // 自动运行最佳实践测试
        setTimeout(testBestPractices, 1000);
        
        // 初始化
        updatePropMutationCounter();
        addLog('Vue.js Prop变更警告修复测试页面已加载');
        addLog('控制台监控已启动，将自动检测prop变更警告');
        addLog('点击"打开中优先级组件演示"进行实际测试');
    </script>
</body>
</html>
